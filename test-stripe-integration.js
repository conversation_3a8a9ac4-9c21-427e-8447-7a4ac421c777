#!/usr/bin/env node

/**
 * Stripe Integration Test Script for Snapback Server
 * 
 * This script tests the Stripe integration by:
 * 1. Creating a payment intent for a standard license
 * 2. Creating a checkout session for an extended license
 * 3. Verifying webhook endpoint configuration
 * 
 * Run with: node test-stripe-integration.js
 */

import Stripe from 'stripe';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, 'apps/server/.env') });

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-06-30.basil',
});

// Pricing configuration (matches your server code)
const PRICING = {
  trial: 0,
  standard: 499, // $4.99 in cents
  extended: 999, // $9.99 in cents
  additionalDevice: 199, // $1.99 per additional device
};

async function testStripeIntegration() {
  console.log('🧪 Testing Stripe Integration for Snapback Server\n');

  try {
    // Test 1: Create Payment Intent (Embedded Flow)
    console.log('1️⃣ Testing Payment Intent Creation...');
    const paymentIntent = await stripe.paymentIntents.create({
      amount: PRICING.standard,
      currency: 'usd',
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        licenseType: 'standard',
        additionalDevices: '0',
        email: '<EMAIL>',
        deviceId: '',
        flow_type: 'embedded',
      },
    });
    
    console.log(`✅ Payment Intent created: ${paymentIntent.id}`);
    console.log(`   Amount: $${paymentIntent.amount / 100}`);
    console.log(`   Status: ${paymentIntent.status}\n`);

    // Test 2: Create Checkout Session (Redirect Flow)
    console.log('2️⃣ Testing Checkout Session Creation...');
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: 'Extended License',
              description: 'License for 5 devices',
            },
            unit_amount: PRICING.extended,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: 'https://yourapp.com/success?session_id={CHECKOUT_SESSION_ID}',
      cancel_url: 'https://yourapp.com/cancel',
      customer_email: '<EMAIL>',
      metadata: {
        licenseType: 'extended',
        additionalDevices: '0',
        email: '<EMAIL>',
        deviceId: '',
        flow_type: 'checkout',
      },
    });

    console.log(`✅ Checkout Session created: ${session.id}`);
    console.log(`   URL: ${session.url}`);
    console.log(`   Amount: $${session.amount_total / 100}\n`);

    // Test 3: List Webhook Endpoints
    console.log('3️⃣ Checking Webhook Configuration...');
    const webhooks = await stripe.webhookEndpoints.list({ limit: 10 });
    
    if (webhooks.data.length === 0) {
      console.log('⚠️  No webhook endpoints configured');
      console.log('   Please create a webhook endpoint in Stripe Dashboard');
      console.log('   URL: https://your-domain.com/api/payments/webhook');
      console.log('   Events: payment_intent.succeeded, checkout.session.completed\n');
    } else {
      console.log(`✅ Found ${webhooks.data.length} webhook endpoint(s):`);
      webhooks.data.forEach((webhook, index) => {
        console.log(`   ${index + 1}. ${webhook.url}`);
        console.log(`      Status: ${webhook.status}`);
        console.log(`      Events: ${webhook.enabled_events.join(', ')}`);
      });
      console.log();
    }

    // Test 4: Account Information
    console.log('4️⃣ Account Information...');
    const account = await stripe.accounts.retrieve();
    console.log(`✅ Account ID: ${account.id}`);
    console.log(`   Business Name: ${account.business_profile?.name || 'Not set'}`);
    console.log(`   Country: ${account.country}`);
    console.log(`   Charges Enabled: ${account.charges_enabled}`);
    console.log(`   Payouts Enabled: ${account.payouts_enabled}\n`);

    console.log('🎉 All tests completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Update your .env file with the correct Stripe keys');
    console.log('2. Create webhook endpoint if not already configured');
    console.log('3. Test your server endpoints with these payment methods');
    console.log('4. Update your frontend to use the new public key');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'api_key_invalid') {
      console.log('\n💡 Solution: Update STRIPE_SECRET_KEY in your .env file');
    } else if (error.code === 'testmode_charges_only') {
      console.log('\n💡 Note: You are using test mode keys (this is expected for development)');
    }
  }
}

// Run the test
testStripeIntegration();

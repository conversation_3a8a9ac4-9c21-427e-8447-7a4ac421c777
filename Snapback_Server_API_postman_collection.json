{"info": {"_postman_id": "snapback-server-api-collection", "name": "Snapback Server API", "description": "Complete API collection for Snapback Server with license management and payment processing endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "License Management", "item": [{"name": "Create Trial License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"licenseType\": \"trial\",\n  \"deviceId\": \"ABC123DEF456GHI789JKL012\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"John's MacBook Pro\",\n    \"deviceType\": \"MacBook Pro\",\n    \"deviceModel\": \"14-inch M2 2023\",\n    \"operatingSystem\": \"macOS 14.1\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"3024x1964\",\n    \"totalMemory\": \"16 GB\",\n    \"userNickname\": \"Work Laptop\",\n    \"location\": \"Office\",\n    \"notes\": \"Primary development machine\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/licenses/create", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "create"]}}}, {"name": "Create Standard License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"licenseType\": \"standard\",\n  \"deviceId\": \"XYZ789ABC123DEF456GHI012\",\n  \"stripePaymentIntentId\": \"pi_**********abcdef\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Sarah's iMac\",\n    \"deviceType\": \"iMac\",\n    \"deviceModel\": \"24-inch M1 2021\",\n    \"operatingSystem\": \"macOS 13.6\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"4480x2520\",\n    \"totalMemory\": \"32 GB\",\n    \"userNickname\": \"Design Station\",\n    \"location\": \"Home Office\",\n    \"notes\": \"Main design workstation\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/licenses/create", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "create"]}}}, {"name": "Create Extended License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"licenseType\": \"extended\",\n  \"deviceId\": \"MNO456PQR789STU012VWX345\",\n  \"stripePaymentIntentId\": \"pi_abcdef**********\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Team Mac Studio\",\n    \"deviceType\": \"Mac Studio\",\n    \"deviceModel\": \"M2 Ultra 2023\",\n    \"operatingSystem\": \"macOS 14.2\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"6016x3384\",\n    \"totalMemory\": \"128 GB\",\n    \"userNickname\": \"Render Farm\",\n    \"location\": \"Studio\",\n    \"notes\": \"High-performance rendering machine\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/licenses/create", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "create"]}}}, {"name": "Validate License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"deviceId\": \"ABC123DEF456GHI789JKL012\",\n  \"appVersion\": \"1.2.3\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Updated MacBook Pro\",\n    \"deviceType\": \"MacBook Pro\",\n    \"deviceModel\": \"14-inch M2 Pro 2023\",\n    \"operatingSystem\": \"macOS 14.3\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"3024x1964\",\n    \"totalMemory\": \"32 GB\",\n    \"userNickname\": \"Updated Work Laptop\",\n    \"location\": \"Remote Office\",\n    \"notes\": \"Updated with more RAM\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/licenses/validate", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "validate"]}}}, {"name": "Resend License Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/licenses/resend", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "resend"]}}}, {"name": "Upgrade License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"additionalDevices\": 2,\n  \"stripePaymentIntentId\": \"pi_upgrade**********\"\n}"}, "url": {"raw": "{{baseUrl}}/api/licenses/upgrade", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "upgrade"]}}}, {"name": "Get License Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/licenses/status/ABCD1234EFGH5678IJKL9012", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "status", "ABCD1234EFGH5678IJKL9012"]}}}, {"name": "Remove <PERSON>ce", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{deviceToken}}"}], "url": {"raw": "{{baseUrl}}/api/licenses/devices/ABC123DEF456GHI789JKL012", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "devices", "ABC123DEF456GHI789JKL012"]}}}, {"name": "Update <PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{deviceToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"ABC123DEF456GHI789JKL012\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Updated MacBook Pro Name\",\n    \"deviceType\": \"MacBook Pro\",\n    \"deviceModel\": \"16-inch M2 Max 2023\",\n    \"operatingSystem\": \"macOS 14.4\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"3456x2234\",\n    \"totalMemory\": \"64 GB\",\n    \"userNickname\": \"Beast Machine\",\n    \"location\": \"New Office\",\n    \"notes\": \"Upgraded to 16-inch model with more power\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/licenses/devices/metadata", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "devices", "metadata"]}}}]}, {"name": "Payment Processing", "item": [{"name": "Get Pricing Information", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payments/pricing", "host": ["{{baseUrl}}"], "path": ["api", "payments", "pricing"]}}}, {"name": "Create Payment Intent (Standard)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"licenseType\": \"standard\",\n  \"additionalDevices\": 0,\n  \"email\": \"<EMAIL>\",\n  \"deviceId\": \"PAYMENT123DEVICE456ID789\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/create-payment-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-payment-intent"]}}}, {"name": "Create Payment Intent (Extended)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"licenseType\": \"extended\",\n  \"additionalDevices\": 2,\n  \"email\": \"<EMAIL>\",\n  \"deviceId\": \"EXTENDED123DEVICE456ID789\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/create-payment-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-payment-intent"]}}}, {"name": "Create Checkout Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"licenseType\": \"standard\",\n  \"additionalDevices\": 0,\n  \"email\": \"<EMAIL>\",\n  \"deviceId\": \"CHECKOUT123DEVICE456ID789\",\n  \"successUrl\": \"https://yourapp.com/success\",\n  \"cancelUrl\": \"https://yourapp.com/cancel\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/create-checkout-session", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-checkout-session"]}}}, {"name": "Create Upgrade Payment Intent", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"additionalDevices\": 3\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/create-upgrade-payment-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-upgrade-payment-intent"]}}}, {"name": "Get Checkout Session Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payments/checkout-session/cs_test_**********abcdef", "host": ["{{baseUrl}}"], "path": ["api", "payments", "checkout-session", "cs_test_**********abcdef"]}}}, {"name": "Get Payment Intent Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payments/payment-intent/pi_**********abcdef", "host": ["{{baseUrl}}"], "path": ["api", "payments", "payment-intent", "pi_**********abcdef"]}}}, {"name": "Stripe Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "stripe-signature", "value": "t=**********,v1=signature_hash_here"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"evt_**********abcdef\",\n  \"object\": \"event\",\n  \"type\": \"payment_intent.succeeded\",\n  \"data\": {\n    \"object\": {\n      \"id\": \"pi_**********abcdef\",\n      \"object\": \"payment_intent\",\n      \"status\": \"succeeded\",\n      \"metadata\": {\n        \"licenseType\": \"standard\",\n        \"email\": \"<EMAIL>\",\n        \"deviceId\": \"WEBHOOK123DEVICE456ID789\"\n      }\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/webhook", "host": ["{{baseUrl}}"], "path": ["api", "payments", "webhook"]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "deviceToken", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.example.token", "type": "string"}]}
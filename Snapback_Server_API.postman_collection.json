{"info": {"_postman_id": "8a9b32cd-04ce-4138-a422-5aa313bc7f51", "name": "Snapback Server API", "description": "Complete API collection for Snapback Server with license management and payment processing endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "32877280"}, "item": [{"name": "License Management", "item": [{"name": "Create Trial License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"licenseType\": \"trial\",\n  \"deviceId\": \"a1b2c3d4e5f67890********************abcdef********************1234\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"John's MacBook Pro\",\n    \"deviceType\": \"MacBook Pro\",\n    \"deviceModel\": \"14-inch M2 2023\",\n    \"operatingSystem\": \"macOS 14.1\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"3024x1964\",\n    \"totalMemory\": \"16 GB\",\n    \"userNickname\": \"Work Laptop\",\n    \"location\": \"Office\",\n    \"notes\": \"Primary development machine\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/create", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "create"]}}, "response": []}, {"name": "Create Standard License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"licenseType\": \"standard\",\n  \"deviceId\": \"b2c3d4e5f67890********************abcdef********************1234a\",\n  \"stripePaymentIntentId\": \"pi_**********abcdef\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Sarah's iMac\",\n    \"deviceType\": \"iMac\",\n    \"deviceModel\": \"24-inch M1 2021\",\n    \"operatingSystem\": \"macOS 13.6\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"4480x2520\",\n    \"totalMemory\": \"32 GB\",\n    \"userNickname\": \"Design Station\",\n    \"location\": \"Home Office\",\n    \"notes\": \"Main design workstation\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/create", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "create"]}}, "response": []}, {"name": "Create Extended License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"licenseType\": \"extended\",\n  \"deviceId\": \"c3d4e5f67890********************abcdef********************1234ab\",\n  \"stripePaymentIntentId\": \"pi_abcdef**********\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Team Mac Studio\",\n    \"deviceType\": \"Mac Studio\",\n    \"deviceModel\": \"M2 Ultra 2023\",\n    \"operatingSystem\": \"macOS 14.2\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"6016x3384\",\n    \"totalMemory\": \"128 GB\",\n    \"userNickname\": \"Render Farm\",\n    \"location\": \"Studio\",\n    \"notes\": \"High-performance rendering machine\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/create", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "create"]}}, "response": []}, {"name": "Validate License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"deviceId\": \"a1b2c3d4e5f67890********************abcdef********************1234\",\n  \"appVersion\": \"1.2.3\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Updated MacBook Pro\",\n    \"deviceType\": \"MacBook Pro\",\n    \"deviceModel\": \"14-inch M2 Pro 2023\",\n    \"operatingSystem\": \"macOS 14.3\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"3024x1964\",\n    \"totalMemory\": \"32 GB\",\n    \"userNickname\": \"Updated Work Laptop\",\n    \"location\": \"Remote Office\",\n    \"notes\": \"Updated with more RAM\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/validate", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "validate"]}}, "response": []}, {"name": "Resend License Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/resend", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "resend"]}}, "response": []}, {"name": "Upgrade License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"additionalDevices\": 2,\n  \"stripePaymentIntentId\": \"pi_upgrade**********\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/upgrade", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "upgrade"]}}, "response": []}, {"name": "Get License Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/licenses/status/ABCD1234EFGH5678IJKL9012", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "status", "ABCD1234EFGH5678IJKL9012"]}}, "response": []}, {"name": "Remove <PERSON>ce", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{deviceToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/licenses/devices/a1b2c3d4e5f67890********************abcdef********************1234", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "devices", "a1b2c3d4e5f67890********************abcdef********************1234"]}}, "response": []}, {"name": "Update <PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{deviceToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"a1b2c3d4e5f67890********************abcdef********************1234\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Updated MacBook Pro Name\",\n    \"deviceType\": \"MacBook Pro\",\n    \"deviceModel\": \"16-inch M2 Max 2023\",\n    \"operatingSystem\": \"macOS 14.4\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"3456x2234\",\n    \"totalMemory\": \"64 GB\",\n    \"userNickname\": \"Beast Machine\",\n    \"location\": \"New Office\",\n    \"notes\": \"Upgraded to 16-inch model with more power\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/devices/metadata", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "devices", "metadata"]}}, "response": []}]}, {"name": "Payment Processing", "item": [{"name": "Get Pricing Information", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payments/pricing", "host": ["{{baseUrl}}"], "path": ["api", "payments", "pricing"]}}, "response": []}, {"name": "Create Payment Intent (Standard)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseType\": \"standard\",\n  \"additionalDevices\": 0,\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/payments/create-payment-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-payment-intent"]}}, "response": []}, {"name": "Create Payment Intent (Extended)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseType\": \"extended\",\n  \"additionalDevices\": 2,\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/payments/create-payment-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-payment-intent"]}}, "response": []}, {"name": "Create Checkout Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseType\": \"standard\",\n  \"additionalDevices\": 0,\n  \"email\": \"<EMAIL>\",\n  \"deviceId\": \"d4e5f67890********************abcdef********************1234abc\",\n  \"successUrl\": \"https://yourapp.com/success\",\n  \"cancelUrl\": \"https://yourapp.com/cancel\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/payments/create-checkout-session", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-checkout-session"]}}, "response": []}, {"name": "Create Upgrade Payment Intent", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"additionalDevices\": 3\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/payments/create-upgrade-payment-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-upgrade-payment-intent"]}}, "response": []}, {"name": "Get Checkout Session Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payments/checkout-session/cs_test_**********abcdef", "host": ["{{baseUrl}}"], "path": ["api", "payments", "checkout-session", "cs_test_**********abcdef"]}}, "response": []}, {"name": "Get Payment Intent Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payments/payment-intent/pi_**********abcdef", "host": ["{{baseUrl}}"], "path": ["api", "payments", "payment-intent", "pi_**********abcdef"]}}, "response": []}, {"name": "Stripe Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "stripe-signature", "value": "t=**********,v1=signature_hash_here", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"evt_**********abcdef\",\n  \"object\": \"event\",\n  \"type\": \"payment_intent.succeeded\",\n  \"data\": {\n    \"object\": {\n      \"id\": \"pi_**********abcdef\",\n      \"object\": \"payment_intent\",\n      \"status\": \"succeeded\",\n      \"amount\": 499,\n      \"currency\": \"usd\",\n      \"metadata\": {\n        \"licenseType\": \"standard\",\n        \"additionalDevices\": \"0\",\n        \"email\": \"<EMAIL>\",\n        \"deviceId\": \"\",\n        \"flow_type\": \"embedded\"\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/payments/webhook", "host": ["{{baseUrl}}"], "path": ["api", "payments", "webhook"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "deviceToken", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.example.token", "type": "string"}]}
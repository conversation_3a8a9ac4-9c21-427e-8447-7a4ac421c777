# 🚀 Stripe Setup Guide for Snapback Server

This guide will help you configure Stripe for the snapback-server license management system.

## 📋 Prerequisites

- Stripe account (create at [stripe.com](https://stripe.com) if needed)
- Access to Stripe Dashboard
- Domain name for webhook endpoint (for production)

## 🔧 Step-by-Step Setup

### 1. Get Stripe API Keys

1. **Login to Stripe Dashboard**: https://dashboard.stripe.com
2. **Navigate to**: Developers > API keys
3. **Copy these keys**:
   - **Publishable key**: `pk_test_...` (starts with pk_test_ for test mode)
   - **Secret key**: `sk_test_...` (starts with sk_test_ for test mode)

### 2. Create Products in Stripe Dashboard

Navigate to **Products** in your Stripe Dashboard and create these products:

#### Product 1: Standard License
- **Name**: `Snapback Standard License`
- **Description**: `Standard license for 2 devices`
- **Pricing**: 
  - **Price**: `$4.99 USD`
  - **Billing**: `One time`
  - **Tax behavior**: `Exclusive` (recommended)

#### Product 2: Extended License  
- **Name**: `Snapback Extended License`
- **Description**: `Extended license for 5 devices`
- **Pricing**:
  - **Price**: `$9.99 USD` 
  - **Billing**: `One time`
  - **Tax behavior**: `Exclusive` (recommended)

#### Product 3: Additional Device
- **Name**: `Snapback Additional Device`
- **Description**: `Add one additional device to your license`
- **Pricing**:
  - **Price**: `$1.99 USD`
  - **Billing**: `One time` 
  - **Tax behavior**: `Exclusive` (recommended)

### 3. Configure Webhook Endpoint

1. **Navigate to**: Developers > Webhooks
2. **Click**: "Add endpoint"
3. **Endpoint URL**: 
   - **Development**: `https://your-ngrok-url.ngrok.io/api/payments/webhook`
   - **Production**: `https://your-domain.com/api/payments/webhook`
4. **Select Events**:
   - `payment_intent.succeeded`
   - `checkout.session.completed`
5. **Click**: "Add endpoint"
6. **Copy**: The webhook signing secret (`whsec_...`)

### 4. Update Environment Variables

Update your `apps/server/.env` file:

```bash
# Stripe Configuration - Snapback Server
STRIPE_SECRET_KEY=sk_test_YOUR_SECRET_KEY_HERE
STRIPE_PUBLIC_KEY=pk_test_YOUR_PUBLIC_KEY_HERE
STRIPE_WEBHOOK_SECRET=whsec_YOUR_WEBHOOK_SECRET_HERE

# Allow test keys in development
ALLOW_TEST_STRIPE_KEYS=true
```

### 5. Test the Integration

Run the test script to verify everything works:

```bash
# Install dependencies if needed
npm install stripe dotenv

# Run the test script
node test-stripe-integration.js
```

Expected output:
```
🧪 Testing Stripe Integration for Snapback Server

1️⃣ Testing Payment Intent Creation...
✅ Payment Intent created: pi_1234567890
   Amount: $4.99
   Status: requires_payment_method

2️⃣ Testing Checkout Session Creation...
✅ Checkout Session created: cs_1234567890
   URL: https://checkout.stripe.com/pay/cs_1234567890
   Amount: $9.99

3️⃣ Checking Webhook Configuration...
✅ Found 1 webhook endpoint(s):
   1. https://your-domain.com/api/payments/webhook
      Status: enabled
      Events: payment_intent.succeeded, checkout.session.completed

4️⃣ Account Information...
✅ Account ID: acct_1234567890
   Business Name: Your Business
   Country: US
   Charges Enabled: true
   Payouts Enabled: true

🎉 All tests completed successfully!
```

## 🧪 Testing Your Server Endpoints

Once configured, test these endpoints:

### Create Payment Intent (Embedded Flow)
```bash
curl -X POST http://localhost:3000/api/payments/create-payment-intent \
  -H "Content-Type: application/json" \
  -d '{
    "licenseType": "standard",
    "additionalDevices": 0,
    "email": "<EMAIL>"
  }'
```

### Create Checkout Session (Redirect Flow)
```bash
curl -X POST http://localhost:3000/api/payments/create-checkout-session \
  -H "Content-Type: application/json" \
  -d '{
    "licenseType": "extended",
    "additionalDevices": 0,
    "email": "<EMAIL>",
    "successUrl": "https://yourapp.com/success",
    "cancelUrl": "https://yourapp.com/cancel"
  }'
```

## 🔒 Security Notes

### Development vs Production

**Test Mode (Development)**:
- Keys start with `pk_test_` and `sk_test_`
- No real money is charged
- Use test card numbers: `****************`

**Live Mode (Production)**:
- Keys start with `pk_live_` and `sk_live_`
- Real money is charged
- Remove `ALLOW_TEST_STRIPE_KEYS=true` from production env

### Environment Variables Security

- **Never commit** `.env` files to version control
- **Use different keys** for development and production
- **Rotate keys** if compromised
- **Use webhook secrets** to verify webhook authenticity

## 🚨 Troubleshooting

### Common Issues

1. **"Invalid API Key"**
   - Check that your secret key starts with `sk_test_` or `sk_live_`
   - Ensure no extra spaces in the key

2. **"Webhook signature verification failed"**
   - Verify webhook secret matches Stripe dashboard
   - Check that webhook URL is accessible

3. **"Cannot use test keys in production"**
   - Add `ALLOW_TEST_STRIPE_KEYS=true` to development env
   - Use live keys for production

4. **Payment Intent creation fails**
   - Verify amount is in cents (499 = $4.99)
   - Check that currency is supported

## 📞 Support

- **Stripe Documentation**: https://stripe.com/docs
- **Stripe Dashboard**: https://dashboard.stripe.com
- **Test Cards**: https://stripe.com/docs/testing#cards

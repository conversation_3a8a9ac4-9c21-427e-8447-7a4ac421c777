// Removed createId import - now using generateLicenseKey from utils
import type { Request } from "express";
import { Router } from "express";
import z from "zod";
import type { License, Device } from "prisma/generated/client";
import type { LogContext } from "@/utils/logger";
import {
	createLicenseLimit,
	resendLicenseLimit,
	validateLicenseLimit,
} from "@/middleware/rate-limit";
import {
	createLicenseSchema,
	resendLicenseSchema,
	validateLicenseSchema,
	updateDeviceMetadataSchema,
} from "@/schemas";
import stripe from "@/services/stripe";
import { sendLicenseEmail } from "@/templates";
import { generateDeviceToken, generateLicenseKey, hashDeviceId, verifyDeviceToken } from "@/utils";

/**
 * Calculate the number of days remaining until license expiration
 * @param expiresAt - License expiration date (null for lifetime licenses)
 * @returns Number of days remaining (0 if expired or lifetime license)
 */
function calculateDaysRemaining(expiresAt: Date | null): number {
	if (!expiresAt) {
		// Lifetime license - return 0 for consistency
		return 0;
	}

	const now = new Date();
	const timeDiff = expiresAt.getTime() - now.getTime();

	if (timeDiff <= 0) {
		// Expired license
		return 0;
	}

	// Convert milliseconds to days and round up
	const daysRemaining = Math.ceil(timeDiff / (24 * 60 * 60 * 1000));
	return Math.max(0, daysRemaining);
}
import {
	ErrorCode,
	handleDatabaseError,
	handleStripeError,
	handleZodError,
	LicenseErrors,
	PaymentErrors,
	sendErrorResponse,
	type PrismaError,
	type StripeError,
} from "@/utils/errors";
import { Logger, EndpointPrefix, requestLoggingMiddleware, extractRequestContext } from "@/utils/logger";
import prisma from "../../../prisma";

// Advanced type definitions for API responses
type LicenseWithDevices = License & { devices: Device[] };
type ApiResponseData =
	| { license: LicenseWithDevices; trialDaysRemaining?: number }
	| { licenses: License[] }
	| { devices: Device[] }
	| { message: string }
	| { error: string }
	| Record<string, unknown>
	| unknown;

// Type for sanitized response data
type SanitizedResponseData = Record<string, unknown>;

/**
 * Sanitize response data for logging by removing or masking sensitive information
 * Uses advanced TypeScript generics to maintain type safety while sanitizing
 */
function sanitizeResponseForLogging<T extends ApiResponseData>(responseData: T): SanitizedResponseData {
	if (!responseData || typeof responseData !== 'object') {
		return responseData as SanitizedResponseData;
	}

	const sanitized = { ...responseData } as Record<string, unknown>;

	// Mask license keys for security (show first 4 and last 4 characters)
	if (sanitized.licenseKey && typeof sanitized.licenseKey === 'string') {
		const key = sanitized.licenseKey;
		if (key.length > 8) {
			sanitized.licenseKey = `${key.substring(0, 4)}****${key.substring(key.length - 4)}`;
		} else {
			sanitized.licenseKey = '****';
		}
	}

	// Sanitize nested objects
	if (sanitized.license && typeof sanitized.license === 'object') {
		sanitized.license = sanitizeResponseForLogging(sanitized.license as ApiResponseData);
	}

	// Sanitize arrays of licenses or devices using proper typing
	if (Array.isArray(sanitized.licenses)) {
		sanitized.licenses = sanitized.licenses.map((license: unknown) =>
			sanitizeResponseForLogging(license as ApiResponseData)
		);
	}
	if (Array.isArray(sanitized.devices)) {
		sanitized.devices = sanitized.devices.map((device: unknown) =>
			sanitizeResponseForLogging(device as ApiResponseData)
		);
	}

	return sanitized;
}

/**
 * Log API response for debugging purposes
 */
function logApiResponse(
	endpointPrefix: EndpointPrefix,
	statusCode: number,
	responseData: ApiResponseData,
	context: Partial<LogContext>,
	additionalInfo?: string
) {
	const sanitizedResponse = sanitizeResponseForLogging(responseData);
	const logLevel = statusCode >= 400 ? 'error' : 'info';
	const logMessage = `API Response ${statusCode}${additionalInfo ? ` - ${additionalInfo}` : ''}`;

	// Create extended context with response data
	const extendedContext = {
		...context,
		statusCode,
		responseData: sanitizedResponse
	} as Partial<LogContext> & { responseData: SanitizedResponseData };

	if (logLevel === 'error') {
		Logger.error(endpointPrefix, logMessage, extendedContext);
	} else {
		Logger.info(endpointPrefix, logMessage, extendedContext);
	}
}





/**
 * Log trial restriction violation for monitoring
 */
async function logTrialRestrictionViolation(
	email: string,
	deviceId: string | undefined,
	violationType: "email" | "device" | "both",
	req: Request,
): Promise<void> {
	const ipAddress = req.ip || req.socket?.remoteAddress || "unknown";
	const userAgent = req.get("User-Agent") || "unknown";

	await prisma.auditLog.create({
		data: {
			action: "TRIAL_RESTRICTION_VIOLATED",
			details: {
				email: email.toLowerCase(),
				deviceId,
				violationType,
				ipAddress,
				userAgent,
				timestamp: new Date().toISOString(),
			},
			ipAddress,
			userAgent,
		},
	});
}

/**
 * Check if a user can upgrade from trial to paid license
 * This allows users to upgrade their existing trial without restriction
 */
async function canUpgradeFromTrial(email: string): Promise<{
	canUpgrade: boolean;
	existingTrialLicense?: LicenseWithDevices;
}> {
	const existingTrialLicense = await prisma.license.findFirst({
		where: {
			email: email.toLowerCase(),
			licenseType: "trial",
		},
		include: {
			devices: true,
		},
	});

	return {
		canUpgrade: !!existingTrialLicense,
		existingTrialLicense: existingTrialLicense || undefined,
	};
}

/**
 * Get detailed information about trial restrictions for an email/device combination
 * This helps provide better error messages and debugging information
 */
async function getTrialRestrictionDetails(email: string, deviceId?: string): Promise<{
	emailRestriction: {
		restricted: boolean;
		existingLicense?: LicenseWithDevices;
	};
	deviceRestriction: {
		restricted: boolean;
		existingLicense?: LicenseWithDevices;
	};
	recommendations: string[];
}> {
	const emailRestriction: { restricted: boolean; existingLicense?: LicenseWithDevices } = { restricted: false };
	const deviceRestriction: { restricted: boolean; existingLicense?: LicenseWithDevices } = { restricted: false };
	const recommendations: string[] = [];

	// Check email restriction
	const existingTrialByEmail = await prisma.license.findFirst({
		where: {
			email: email.toLowerCase(),
			licenseType: "trial",
		},
		include: {
			devices: true,
		},
	});

	if (existingTrialByEmail) {
		emailRestriction.restricted = true;
		emailRestriction.existingLicense = existingTrialByEmail;

		if (existingTrialByEmail.expiresAt && existingTrialByEmail.expiresAt < new Date()) {
			recommendations.push("Your previous trial has expired. Consider purchasing a paid license.");
		} else {
			recommendations.push("You already have an active trial license. Check your email for the license key.");
		}
	}

	// Check device restriction
	if (deviceId) {
		// Get all trial licenses with their devices
		const trialLicenses = await prisma.license.findMany({
			where: {
				licenseType: "trial",
			},
			include: {
				devices: true,
			},
		});

		// Check if the deviceId matches any existing device by hashing with stored salts
		let existingTrialByDevice = null;
		for (const license of trialLicenses) {
			for (const device of license.devices) {
				const { hash: testHash } = hashDeviceId(deviceId, device.salt);
				if (testHash === device.deviceHash) {
					existingTrialByDevice = license;
					break;
				}
			}
			if (existingTrialByDevice) break;
		}

		if (existingTrialByDevice) {
			deviceRestriction.restricted = true;
			deviceRestriction.existingLicense = existingTrialByDevice;

			if (existingTrialByDevice.email.toLowerCase() === email.toLowerCase()) {
				recommendations.push("This device is already registered with your trial license.");
			} else {
				recommendations.push("This device has already been used for a trial license with a different email address.");
			}
		}
	}

	// Add general recommendations
	if (emailRestriction.restricted || deviceRestriction.restricted) {
		recommendations.push("Consider purchasing a paid license for continued access.");
		recommendations.push("Contact support if you believe this is an error.");
	}

	return {
		emailRestriction,
		deviceRestriction,
		recommendations,
	};
}

const router: Router = Router();

/**
 * @route POST /api/licenses/create
 * @description Create a new license (trial or paid)
 * @access Public
 * @rateLimit 5 requests per 15 minutes per IP
 *
 * @param {Object} req.body - Request body
 * @param {string} req.body.email - User's email address
 * @param {string} req.body.licenseType - Type of license (trial, standard, extended)
 * @param {string} req.body.deviceId - Unique device identifier
 * @param {string} [req.body.stripePaymentIntentId] - Stripe payment intent ID (required for paid licenses)
 *
 * @returns {Object} 201 - License created successfully
 * @returns {string} returns.licenseKey - Generated license key
 * @returns {string} returns.licenseType - Type of license created
 * @returns {Date} returns.expiresAt - License expiration date (null for lifetime licenses)
 * @returns {number} returns.maxDevices - Maximum number of devices allowed
 * @returns {number} returns.trialDaysRemaining - Days remaining until expiration (0 if expired or lifetime)
 * @returns {string} returns.deviceToken - JWT token for device authentication
 *
 * @returns {Object} 400 - Validation error or payment incomplete
 * @returns {Object} 409 - License already exists for email
 * @returns {Object} 429 - Rate limit exceeded
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * // Create trial license
 * POST /api/licenses/create
 * {
 *   "email": "<EMAIL>",
 *   "licenseType": "trial",
 *   "deviceId": "abc123def456"
 * }
 *
 * @example
 * // Create paid license
 * POST /api/licenses/create
 * {
 *   "email": "<EMAIL>",
 *   "licenseType": "standard",
 *   "deviceId": "abc123def456",
 *   "stripePaymentIntentId": "pi_1234567890"
 * }
 */
router.post("/create", createLicenseLimit, requestLoggingMiddleware(EndpointPrefix.LICENSE_CREATE), async (req, res) => {
	try {
		const context = extractRequestContext(req);
		Logger.info(EndpointPrefix.LICENSE_CREATE, "Processing license creation request", context);

		const { email, licenseType, deviceId, stripePaymentIntentId, deviceMetadata } =
			createLicenseSchema.parse(req.body);

		Logger.info(EndpointPrefix.LICENSE_CREATE, `License creation requested: ${licenseType} for ${email}`, {
			...context,
			body: { email, licenseType, deviceId: deviceId ? '[PROVIDED]' : '[NOT_PROVIDED]', stripePaymentIntentId: stripePaymentIntentId ? '[PROVIDED]' : '[NOT_PROVIDED]' }
		});

		// Trial licenses require deviceId for immediate device registration
		if (licenseType === "trial" && !deviceId) {
			Logger.warn(EndpointPrefix.LICENSE_CREATE, "Trial license creation failed: deviceId required", context);
			return sendErrorResponse(
				res,
				"Device ID is required for trial licenses",
				400,
				ErrorCode.VALIDATION_ERROR,
				"Trial licenses must include a deviceId for immediate device registration",
			);
		}

		// For paid licenses, verify payment
		if (licenseType !== "trial" && stripePaymentIntentId) {
			Logger.info(EndpointPrefix.LICENSE_CREATE, "Verifying payment for paid license", {
				...context,
				body: { licenseType, stripePaymentIntentId: '[PROVIDED]' }
			});

			const paymentIntent = await stripe.paymentIntents.retrieve(
				stripePaymentIntentId,
			);

			Logger.info(EndpointPrefix.LICENSE_CREATE, `Payment intent status: ${paymentIntent.status}`, {
				...context,
				body: { paymentIntentId: '[PROVIDED]', status: paymentIntent.status }
			});

			if (paymentIntent.status !== "succeeded") {
				Logger.error(EndpointPrefix.LICENSE_CREATE, `Payment not completed: ${paymentIntent.status}`, context);
				return PaymentErrors.incomplete(res, paymentIntent.status);
			}

			Logger.info(EndpointPrefix.LICENSE_CREATE, "Payment verification successful", context);
		}

		// For trial licenses, check restrictions to prevent abuse
		if (licenseType === "trial") {
			Logger.info(EndpointPrefix.LICENSE_CREATE, "Checking trial license restrictions", {
				...context,
				body: { email, deviceId: deviceId ? '[PROVIDED]' : '[NOT_PROVIDED]' }
			});

			const restrictionDetails = await getTrialRestrictionDetails(email, deviceId);
			const canCreateTrial = !restrictionDetails.emailRestriction.restricted && !restrictionDetails.deviceRestriction.restricted;

			Logger.debug(EndpointPrefix.LICENSE_CREATE, "Trial restriction check results", {
				...context,
				body: {
					canCreateTrial,
					emailRestricted: restrictionDetails.emailRestriction.restricted,
					deviceRestricted: restrictionDetails.deviceRestriction.restricted,
					recommendations: restrictionDetails.recommendations
				}
			});

			if (!canCreateTrial) {
				// Log the violation attempt
				const violationType = restrictionDetails.emailRestriction.restricted && restrictionDetails.deviceRestriction.restricted
					? "both"
					: restrictionDetails.emailRestriction.restricted
						? "email"
						: "device";

				Logger.warn(EndpointPrefix.LICENSE_CREATE, `Trial license creation blocked: ${violationType} restriction`, {
					...context,
					body: {
						email,
						violationType,
						emailRestricted: restrictionDetails.emailRestriction.restricted,
						deviceRestricted: restrictionDetails.deviceRestriction.restricted,
						recommendations: restrictionDetails.recommendations
					}
				});

				await logTrialRestrictionViolation(email, deviceId, violationType, req);

				// Return appropriate error based on which restriction was violated
				if (restrictionDetails.emailRestriction.restricted && restrictionDetails.deviceRestriction.restricted) {
					return LicenseErrors.trialAlreadyUsed(res, email, deviceId || "unknown");
				} else if (restrictionDetails.emailRestriction.restricted) {
					return LicenseErrors.trialAlreadyUsedEmail(res, email);
				} else {
					return LicenseErrors.trialAlreadyUsedDevice(res, deviceId || "unknown");
				}
			}

			Logger.info(EndpointPrefix.LICENSE_CREATE, "Trial license restrictions passed", context);
		} else {
			// For paid licenses, check for existing licenses and handle upgrades
			const existingLicense = await prisma.license.findFirst({
				where: {
					email: email.toLowerCase(),
					OR: [
						{ expiresAt: null }, // Permanent license
						{ expiresAt: { gt: new Date() } }, // Non-expired license
					],
				},
				include: {
					devices: true,
				},
			});

			if (existingLicense) {
				// If user has a trial license, allow upgrade to paid license
				if (existingLicense.licenseType === "trial") {
					console.log(`Upgrading trial license to ${licenseType} for ${email}`);

					// Log the upgrade attempt
					await prisma.auditLog.create({
						data: {
							action: "LICENSE_CREATED",
							details: {
								upgradeFrom: "trial",
								upgradeTo: licenseType,
								email,
								existingLicenseKey: existingLicense.licenseKey,
								stripePaymentIntentId,
							},
						},
					});
				} else {
					// User already has a paid license
					return LicenseErrors.alreadyExists(res, existingLicense.licenseType);
				}
			}
		}

		// Check if this is an upgrade from trial
		const upgradeInfo = await canUpgradeFromTrial(email);
		let license: { id: string; licenseKey: string; licenseType: string; maxDevices: number; expiresAt: Date | null };
		let finalMaxDevices: number;
		let finalExpiresAt: Date | null = null;

		if (licenseType !== "trial" && upgradeInfo.canUpgrade && upgradeInfo.existingTrialLicense) {
			// Upgrade existing trial license instead of creating new one
			const existingLicense = upgradeInfo.existingTrialLicense;

			// Set license parameters based on new type
			switch (licenseType) {
				case "standard":
					finalMaxDevices = 2;
					finalExpiresAt = null; // Lifetime license
					break;
				case "extended":
					finalMaxDevices = 5;
					finalExpiresAt = null; // Lifetime license
					break;
				default:
					return sendErrorResponse(
						res,
						"Invalid license type",
						400,
						ErrorCode.VALIDATION_ERROR,
						"The specified license type is not supported",
					);
			}

			// Update the existing trial license to paid license
			license = await prisma.license.update({
				where: { id: existingLicense.id },
				data: {
					licenseType,
					maxDevices: finalMaxDevices,
					expiresAt: finalExpiresAt, // Set to null for lifetime paid licenses
					stripePaymentIntentId,
					updatedAt: new Date(),
				},
			});

			console.log(`Successfully upgraded trial license ${existingLicense.licenseKey} to ${licenseType}`);
		} else {
			// Create new license (for trial or when no existing trial exists)
			const licenseKey = generateLicenseKey();

			// Set license parameters based on type
			switch (licenseType) {
				case "trial":
					finalMaxDevices = 1;
					finalExpiresAt = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000); // 14 days
					break;
				case "standard":
					finalMaxDevices = 2;
					finalExpiresAt = null; // Lifetime license
					break;
				case "extended":
					finalMaxDevices = 5;
					finalExpiresAt = null; // Lifetime license
					break;
				default:
					return sendErrorResponse(
						res,
						"Invalid license type",
						400,
						ErrorCode.VALIDATION_ERROR,
						"The specified license type is not supported",
					);
			}

			// Create new license
			license = await prisma.license.create({
				data: {
					licenseKey,
					email: email.toLowerCase(),
					licenseType,
					maxDevices: finalMaxDevices,
					expiresAt: finalExpiresAt,
					stripePaymentIntentId,
				},
			});
		}

		// Register the initial device if provided
		if (deviceId) {
			const { hash: deviceHash, salt } = hashDeviceId(deviceId);

			await prisma.device.create({
				data: {
					licenseId: license.id,
					deviceHash,
					salt,
					firstSeen: new Date(),
					lastSeen: new Date(),
					// Include device metadata if provided
					...(deviceMetadata && {
						deviceName: deviceMetadata.deviceName,
						deviceType: deviceMetadata.deviceType,
						deviceModel: deviceMetadata.deviceModel,
						operatingSystem: deviceMetadata.operatingSystem,
						architecture: deviceMetadata.architecture,
						screenResolution: deviceMetadata.screenResolution,
						totalMemory: deviceMetadata.totalMemory,
						userNickname: deviceMetadata.userNickname,
						location: deviceMetadata.location,
						notes: deviceMetadata.notes,
					}),
				},
			});
		}

		// Send license email only for paid licenses (standard, extended)
		// Trial licenses are returned directly in the response for immediate use
		if (licenseType !== "trial") {
			try {
				await sendLicenseEmail(email, license.licenseKey, licenseType, finalExpiresAt);
				Logger.info(EndpointPrefix.LICENSE_CREATE, "License email sent successfully", {
					...context,
					body: { email, licenseKey: license.licenseKey, licenseType }
				});
			} catch (emailError) {
				Logger.error(EndpointPrefix.LICENSE_CREATE, "Failed to send license email", {
					...context,
					body: { email, licenseType, error: emailError instanceof Error ? emailError.message : String(emailError) }
				});
				// Continue with license creation even if email fails for paid licenses
			}
		} else {
			Logger.info(EndpointPrefix.LICENSE_CREATE, "Trial license created - email skipped, returning license directly", {
				...context,
				body: { email, licenseKey: license.licenseKey, licenseType }
			});
		}

		// Log license creation
		await prisma.auditLog.create({
			data: {
				action: "LICENSE_CREATED",
				details: {
					licenseKey: license.licenseKey,
					licenseType,
					email,
					maxDevices: finalMaxDevices,
					expiresAt: finalExpiresAt,
					stripePaymentIntentId,
					isUpgrade: upgradeInfo.canUpgrade,
				},
			},
		});

		// Prepare response message based on license type and upgrade status
		let responseMessage: string;
		if (upgradeInfo.canUpgrade) {
			responseMessage = "License upgraded successfully";
		} else if (licenseType === "trial") {
			responseMessage = "Trial license created successfully. You can start using the app immediately with the license key provided below.";
		} else {
			responseMessage = "License created successfully. The license key has been sent to your email address.";
		}

		// Fetch the complete license object with all fields for response
		const completeLicense = await prisma.license.findUnique({
			where: { id: license.id },
			include: {
				devices: {
					select: {
						id: true,
						deviceHash: false, // Don't expose sensitive hash
						salt: false, // Don't expose salt
						firstSeen: true,
						lastSeen: true,
						appVersion: true,
						isActive: true
					}
				}
			}
		});

		if (!completeLicense) {
			throw new Error("Failed to retrieve created license");
		}

		// Calculate days remaining for all license types
		const trialDaysRemaining = calculateDaysRemaining(completeLicense.expiresAt);

		// Prepare complete license response object
		const responseData = {
			message: responseMessage,
			// Complete license object for immediate use
			license: {
				id: completeLicense.id,
				licenseKey: completeLicense.licenseKey,
				email: completeLicense.email,
				licenseType: completeLicense.licenseType,
				maxDevices: completeLicense.maxDevices,
				expiresAt: completeLicense.expiresAt,
				createdAt: completeLicense.createdAt,
				updatedAt: completeLicense.updatedAt,
				stripePaymentIntentId: completeLicense.stripePaymentIntentId,
				devicesUsed: completeLicense.devices.length,
				devices: completeLicense.devices,
				trialDaysRemaining
			},
			// Legacy fields for backward compatibility
			licenseKey: completeLicense.licenseKey,
			licenseType: completeLicense.licenseType,
			maxDevices: completeLicense.maxDevices,
			expiresAt: completeLicense.expiresAt,
			devicesUsed: completeLicense.devices.length,
			trialDaysRemaining,
			// Add helpful information for trial licenses
			...(licenseType === "trial" && {
				trialInfo: {
					emailSent: false,
					immediateAccess: true,
					note: "Trial license is ready for immediate use. No email verification required."
				}
			}),
			// Add helpful information for paid licenses
			...(licenseType !== "trial" && {
				deliveryInfo: {
					emailSent: true,
					note: "License key has been sent to your email address. Please check your inbox."
				}
			})
		};

		// Log the response for debugging
		logApiResponse(
			EndpointPrefix.LICENSE_CREATE,
			201,
			responseData,
			context,
			`License created: ${licenseType} for ${email}`
		);

		res.status(201).json(responseData);
	} catch (error) {
		console.error("Error creating license:", error);

		if (error instanceof z.ZodError) {
			return handleZodError(res, error);
		}

		// Handle database errors
		if (
			error &&
			typeof error === "object" &&
			"code" in error &&
			typeof error.code === "string" &&
			error.code.startsWith("P") &&
			"name" in error &&
			"message" in error
		) {
			return handleDatabaseError(res, error as PrismaError);
		}

		// Handle Stripe errors
		if (
			error &&
			typeof error === "object" &&
			"type" in error &&
			typeof error.type === "string" &&
			error.type.startsWith("Stripe") &&
			"name" in error &&
			"message" in error
		) {
			return handleStripeError(res, error as StripeError);
		}

		sendErrorResponse(
			res,
			"Internal server error",
			500,
			ErrorCode.INTERNAL_ERROR,
			"An unexpected error occurred while creating the license",
		);
	}
});

/**
 * @route POST /api/licenses/validate
 * @description Validate a license key and register/authenticate a device
 * @access Public
 * @rateLimit 30 requests per minute per IP
 *
 * @param {Object} req.body - Request body
 * @param {string} req.body.licenseKey - License key to validate
 * @param {string} req.body.deviceId - Unique device identifier
 * @param {string} req.body.appVersion - Application version making the request
 *
 * @returns {Object} 200 - License validation successful
 * @returns {boolean} returns.valid - Whether the license is valid
 * @returns {string} returns.licenseType - Type of license
 * @returns {Date} returns.expiresAt - License expiration date (null for lifetime)
 * @returns {number} returns.maxDevices - Maximum devices allowed
 * @returns {number} returns.devicesUsed - Current number of registered devices
 * @returns {number} returns.trialDaysRemaining - Days remaining until expiration (0 if expired or lifetime)
 * @returns {string} returns.deviceToken - JWT token for device authentication
 * @returns {boolean} returns.isNewDevice - Whether this is a newly registered device
 *
 * @returns {Object} 400 - Validation error
 * @returns {Object} 403 - License expired or max devices reached
 * @returns {Object} 404 - License not found
 * @returns {Object} 429 - Rate limit exceeded
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * POST /api/licenses/validate
 * {
 *   "licenseKey": "clx1234567890abcdef",
 *   "deviceId": "abc123def456",
 *   "appVersion": "1.0.0"
 * }
 */
router.post("/validate", validateLicenseLimit, requestLoggingMiddleware(EndpointPrefix.LICENSE_VALIDATE), async (req, res) => {
	try {
		const context = extractRequestContext(req);
		Logger.info(EndpointPrefix.LICENSE_VALIDATE, "Processing license validation request", context);

		const { licenseKey, deviceId, appVersion, deviceMetadata } = validateLicenseSchema.parse(
			req.body,
		);

		Logger.info(EndpointPrefix.LICENSE_VALIDATE, "License validation requested", {
			...context,
			body: {
				licenseKey: licenseKey ? `${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}` : '[NOT_PROVIDED]',
				deviceId: deviceId ? '[PROVIDED]' : '[NOT_PROVIDED]',
				appVersion
			}
		});

		// Find license
		const license = await prisma.license.findUnique({
			where: { licenseKey },
			include: { devices: true },
		});

		if (!license) {
			// Log error response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_VALIDATE,
				404,
				{ error: "License not found", code: "LICENSE_NOT_FOUND" },
				context,
				`License not found: ${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}`
			);
			return LicenseErrors.notFound(res);
		}

		// Check expiration
		if (license.expiresAt && license.expiresAt < new Date()) {
			// Log error response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_VALIDATE,
				403,
				{ error: "License expired", code: "LICENSE_EXPIRED" },
				context,
				`License expired: ${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}`
			);
			return LicenseErrors.expired(res);
		}

		// Check if device is already registered
		let existingDevice = null;
		for (const device of license.devices) {
			const { hash: testHash } = hashDeviceId(deviceId, device.salt);
			if (testHash === device.deviceHash) {
				existingDevice = device;
				break;
			}
		}

		if (existingDevice) {
			// Update last seen and device metadata if provided
			const updateData: any = {
				lastSeen: new Date(),
				appVersion: appVersion || existingDevice.appVersion,
			};

			// Add device metadata if provided
			if (deviceMetadata) {
				if (deviceMetadata.deviceName !== undefined) updateData.deviceName = deviceMetadata.deviceName;
				if (deviceMetadata.deviceType !== undefined) updateData.deviceType = deviceMetadata.deviceType;
				if (deviceMetadata.deviceModel !== undefined) updateData.deviceModel = deviceMetadata.deviceModel;
				if (deviceMetadata.operatingSystem !== undefined) updateData.operatingSystem = deviceMetadata.operatingSystem;
				if (deviceMetadata.architecture !== undefined) updateData.architecture = deviceMetadata.architecture;
				if (deviceMetadata.screenResolution !== undefined) updateData.screenResolution = deviceMetadata.screenResolution;
				if (deviceMetadata.totalMemory !== undefined) updateData.totalMemory = deviceMetadata.totalMemory;
				if (deviceMetadata.userNickname !== undefined) updateData.userNickname = deviceMetadata.userNickname;
				if (deviceMetadata.location !== undefined) updateData.location = deviceMetadata.location;
				if (deviceMetadata.notes !== undefined) updateData.notes = deviceMetadata.notes;
			}

			await prisma.device.update({
				where: { id: existingDevice.id },
				data: updateData,
			});

			const deviceToken = generateDeviceToken(
				license.id,
				existingDevice.deviceHash,
			);

			const responseData = {
				valid: true,
				deviceToken,
				licenseType: license.licenseType,
				expiresAt: license.expiresAt,
				maxDevices: license.maxDevices,
				devicesUsed: license.devices.length,
				trialDaysRemaining: calculateDaysRemaining(license.expiresAt),
			};

			// Log the response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_VALIDATE,
				200,
				responseData,
				context,
				`License validated for existing device`
			);

			return res.json(responseData);
		}

		// Check if we can add a new device
		if (license.devices.length >= license.maxDevices) {
			// Log error response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_VALIDATE,
				403,
				{
					error: "Maximum devices reached",
					code: "MAX_DEVICES_REACHED",
					maxDevices: license.maxDevices,
					currentDevices: license.devices.length
				},
				context,
				`Max devices reached: ${license.devices.length}/${license.maxDevices}`
			);
			return LicenseErrors.maxDevicesReached(
				res,
				license.maxDevices,
				license.devices.length,
			);
		}

		// Add new device
		const { hash: newDeviceHash, salt: newSalt } = hashDeviceId(deviceId);

		const newDeviceData: any = {
			licenseId: license.id,
			deviceHash: newDeviceHash,
			salt: newSalt,
			firstSeen: new Date(),
			lastSeen: new Date(),
			appVersion,
		};

		// Add device metadata if provided
		if (deviceMetadata) {
			if (deviceMetadata.deviceName !== undefined) newDeviceData.deviceName = deviceMetadata.deviceName;
			if (deviceMetadata.deviceType !== undefined) newDeviceData.deviceType = deviceMetadata.deviceType;
			if (deviceMetadata.deviceModel !== undefined) newDeviceData.deviceModel = deviceMetadata.deviceModel;
			if (deviceMetadata.operatingSystem !== undefined) newDeviceData.operatingSystem = deviceMetadata.operatingSystem;
			if (deviceMetadata.architecture !== undefined) newDeviceData.architecture = deviceMetadata.architecture;
			if (deviceMetadata.screenResolution !== undefined) newDeviceData.screenResolution = deviceMetadata.screenResolution;
			if (deviceMetadata.totalMemory !== undefined) newDeviceData.totalMemory = deviceMetadata.totalMemory;
			if (deviceMetadata.userNickname !== undefined) newDeviceData.userNickname = deviceMetadata.userNickname;
			if (deviceMetadata.location !== undefined) newDeviceData.location = deviceMetadata.location;
			if (deviceMetadata.notes !== undefined) newDeviceData.notes = deviceMetadata.notes;
		}

		await prisma.device.create({
			data: newDeviceData,
		});

		const deviceToken = generateDeviceToken(license.id, newDeviceHash);

		const responseData = {
			valid: true,
			deviceToken,
			licenseType: license.licenseType,
			expiresAt: license.expiresAt,
			maxDevices: license.maxDevices,
			devicesUsed: license.devices.length + 1,
			trialDaysRemaining: calculateDaysRemaining(license.expiresAt),
		};

		// Log the response for debugging
		logApiResponse(
			EndpointPrefix.LICENSE_VALIDATE,
			200,
			responseData,
			context,
			`License validated for new device`
		);

		res.json(responseData);
	} catch (error) {
		console.error("Error validating license:", error);

		if (error instanceof z.ZodError) {
			return res
				.status(400)
				.json({ error: "Invalid input data", details: error.message });
		}

		res.status(500).json({ error: "Internal server error" });
	}
});

/**
 * @route POST /api/licenses/resend
 * @description Resend license information to user's email address
 * @access Public
 * @rateLimit 3 requests per hour per IP
 *
 * @param {Object} req.body - Request body
 * @param {string} req.body.email - Email address to resend license to
 *
 * @returns {Object} 200 - Success response (always returns success for security)
 * @returns {string} returns.message - Confirmation message
 *
 * @returns {Object} 400 - Validation error
 * @returns {Object} 429 - Rate limit exceeded
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * POST /api/licenses/resend
 * {
 *   "email": "<EMAIL>"
 * }
 *
 * @security This endpoint always returns success to prevent email enumeration attacks
 */
router.post("/resend", resendLicenseLimit, requestLoggingMiddleware(EndpointPrefix.LICENSE_RESEND), async (req, res) => {
	try {
		const context = extractRequestContext(req);
		Logger.info(EndpointPrefix.LICENSE_RESEND, "Processing license resend request", context);

		const { email } = resendLicenseSchema.parse(req.body);

		Logger.info(EndpointPrefix.LICENSE_RESEND, `License resend requested for: ${email}`, {
			...context,
			body: { email }
		});

		const license = await prisma.license.findFirst({
			where: {
				email: email.toLowerCase(),
				OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
			},
		});

		if (!license) {
			// Don't reveal if email exists or not for security
			const responseData = {
				message: "If a license exists for this email, it has been sent.",
			};

			// Log the response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_RESEND,
				200,
				responseData,
				context,
				`License resend - no license found for email`
			);

			return res.json(responseData);
		}

		await sendLicenseEmail(
			email,
			license.licenseKey,
			license.licenseType,
			license.expiresAt,
		);

		const responseData = { message: "License sent to email address." };

		// Log the response for debugging
		logApiResponse(
			EndpointPrefix.LICENSE_RESEND,
			200,
			responseData,
			context,
			`License resent successfully`
		);

		res.json(responseData);
	} catch (error) {
		console.error("Error resending license:", error);

		if (error instanceof z.ZodError) {
			return res
				.status(400)
				.json({ error: "Invalid input data", details: error.message });
		}

		res.status(500).json({ error: "Internal server error" });
	}
});

// Upgrade license (add more devices)
router.post("/upgrade", createLicenseLimit, requestLoggingMiddleware(EndpointPrefix.LICENSE_UPGRADE), async (req, res) => {
	try {
		const context = extractRequestContext(req);
		Logger.info(EndpointPrefix.LICENSE_UPGRADE, "Processing license upgrade request", context);

		const { licenseKey, additionalDevices, stripePaymentIntentId } = req.body;

		Logger.info(EndpointPrefix.LICENSE_UPGRADE, "License upgrade requested", {
			...context,
			body: {
				licenseKey: licenseKey ? `${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}` : '[NOT_PROVIDED]',
				additionalDevices,
				stripePaymentIntentId: stripePaymentIntentId ? '[PROVIDED]' : '[NOT_PROVIDED]'
			}
		});

		// Verify payment
		if (stripePaymentIntentId) {
			const paymentIntent = await stripe.paymentIntents.retrieve(
				stripePaymentIntentId,
			);
			if (paymentIntent.status !== "succeeded") {
				return res.status(400).json({ error: "Payment not completed" });
			}
		}

		const license = await prisma.license.findUnique({
			where: { licenseKey },
		});

		if (!license) {
			return res.status(404).json({ error: "License not found" });
		}

		// Update max devices
		await prisma.license.update({
			where: { licenseKey },
			data: {
				maxDevices: license.maxDevices + additionalDevices,
				upgradePaymentIntentId: stripePaymentIntentId,
			},
		});

		res.json({
			message: "License upgraded successfully",
			newMaxDevices: license.maxDevices + additionalDevices,
		});
	} catch (error) {
		console.error("Error upgrading license:", error);
		res.status(500).json({ error: "Internal server error" });
	}
});

/**
 * @route GET /api/licenses/status/:licenseKey
 * @description Get detailed status information for a license
 * @access Public
 * @rateLimit General API rate limit applies
 *
 * @param {string} req.params.licenseKey - License key to check status for
 *
 * @returns {Object} 200 - License status information
 * @returns {string} returns.licenseKey - The license key
 * @returns {string} returns.licenseType - Type of license
 * @returns {string} returns.email - Associated email address
 * @returns {Date} returns.createdAt - License creation date
 * @returns {Date} returns.expiresAt - License expiration date (null for lifetime)
 * @returns {number} returns.maxDevices - Maximum devices allowed
 * @returns {number} returns.devicesUsed - Current number of registered devices
 * @returns {boolean} returns.isExpired - Whether the license has expired
 * @returns {boolean} returns.isActive - Whether the license is active
 * @returns {number} returns.trialDaysRemaining - Days remaining until expiration (0 if expired or lifetime)
 * @returns {Array} returns.devices - Array of registered devices (without sensitive data)
 *
 * @returns {Object} 404 - License not found
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * GET /api/licenses/status/clx1234567890abcdef
 */
router.get("/status/:licenseKey", requestLoggingMiddleware(EndpointPrefix.LICENSE_STATUS), async (req, res) => {
	try {
		const context = extractRequestContext(req);
		Logger.info(EndpointPrefix.LICENSE_STATUS, "Processing license status request", context);

		const { licenseKey } = req.params;

		Logger.info(EndpointPrefix.LICENSE_STATUS, "License status requested", {
			...context,
			params: {
				licenseKey: licenseKey ? `${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}` : '[NOT_PROVIDED]'
			}
		});

		const license = await prisma.license.findUnique({
			where: { licenseKey },
			include: {
				devices: {
					select: {
						id: true,
						firstSeen: true,
						lastSeen: true,
						appVersion: true,
						isActive: true,
						// Exclude sensitive deviceHash and salt
					},
				},
			},
		});

		if (!license) {
			// Log error response for debugging
			logApiResponse(
				EndpointPrefix.LICENSE_STATUS,
				404,
				{ error: "License not found", code: "LICENSE_NOT_FOUND" },
				context,
				`License not found: ${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}`
			);
			return LicenseErrors.notFound(res);
		}

		const isExpired = license.expiresAt
			? license.expiresAt < new Date()
			: false;
		const isActive = !isExpired;

		const responseData = {
			licenseKey: license.licenseKey,
			licenseType: license.licenseType,
			email: license.email,
			createdAt: license.createdAt,
			expiresAt: license.expiresAt,
			maxDevices: license.maxDevices,
			devicesUsed: license.devices.length,
			isExpired,
			isActive,
			trialDaysRemaining: calculateDaysRemaining(license.expiresAt),
			devices: license.devices.map((device) => ({
				id: device.id,
				firstSeen: device.firstSeen,
				lastSeen: device.lastSeen,
				appVersion: device.appVersion,
				isActive: device.isActive,
			})),
		};

		// Log the response for debugging
		logApiResponse(
			EndpointPrefix.LICENSE_STATUS,
			200,
			responseData,
			context,
			`License status retrieved`
		);

		res.json(responseData);
	} catch (error) {
		console.error("Error getting license status:", error);

		sendErrorResponse(
			res,
			"Internal server error",
			500,
			ErrorCode.INTERNAL_ERROR,
			"An unexpected error occurred while retrieving license status",
		);
	}
});

/**
 * @route DELETE /api/licenses/devices/:deviceId
 * @description Remove a device from a license
 * @access Public (requires device token authentication)
 * @rateLimit General API rate limit applies
 *
 * @param {string} req.params.deviceId - Device ID to remove
 * @param {string} req.headers.authorization - Bearer token with device JWT
 *
 * @returns {Object} 200 - Device removed successfully
 * @returns {string} returns.message - Success message
 * @returns {number} returns.devicesRemaining - Number of devices remaining on license
 *
 * @returns {Object} 401 - Invalid or missing device token
 * @returns {Object} 404 - Device not found
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * DELETE /api/licenses/devices/abc123def456
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */
router.delete("/devices/:deviceId", requestLoggingMiddleware(EndpointPrefix.LICENSE_REMOVE_DEVICE), async (req, res) => {
	try {
		const context = extractRequestContext(req);
		Logger.info(EndpointPrefix.LICENSE_REMOVE_DEVICE, "Processing device removal request", context);

		const { deviceId } = req.params;
		const authHeader = req.headers.authorization;

		Logger.info(EndpointPrefix.LICENSE_REMOVE_DEVICE, "Device removal requested", {
			...context,
			params: { deviceId: deviceId ? '[PROVIDED]' : '[NOT_PROVIDED]' },
			authorization: authHeader ? '[PROVIDED]' : '[NOT_PROVIDED]'
		});

		if (!authHeader || !authHeader.startsWith("Bearer ")) {
			return sendErrorResponse(
				res,
				"Missing or invalid authorization header",
				401,
				ErrorCode.UNAUTHORIZED,
				"A valid device token is required",
			);
		}

		const token = authHeader.substring(7);
		const payload = verifyDeviceToken(token) as {
			licenseId: string;
			deviceHash: string;
		} | null;

		if (!payload) {
			return sendErrorResponse(
				res,
				"Invalid device token",
				401,
				ErrorCode.INVALID_TOKEN,
				"The provided device token is invalid or expired",
			);
		}

		// Find the device by trying all possible salts for this license
		const license = await prisma.license.findUnique({
			where: { id: payload.licenseId },
			include: { devices: true },
		});

		if (!license) {
			return sendErrorResponse(
				res,
				"License not found",
				404,
				ErrorCode.NOT_FOUND,
				"The license associated with this device token was not found",
			);
		}

		let deviceToRemove = null;
		for (const device of license.devices) {
			const { hash: testHash } = hashDeviceId(deviceId, device.salt);
			if (testHash === device.deviceHash) {
				deviceToRemove = device;
				break;
			}
		}

		if (!deviceToRemove) {
			return sendErrorResponse(
				res,
				"Device not found",
				404,
				ErrorCode.DEVICE_NOT_REGISTERED,
				"The specified device is not registered with this license",
			);
		}

		// Remove the device
		await prisma.device.delete({
			where: { id: deviceToRemove.id },
		});

		// Log the device removal
		await prisma.auditLog.create({
			data: {
				action: "DEVICE_REMOVED",
				details: {
					licenseId: license.id,
					deviceId: deviceToRemove.id,
					licenseKey: license.licenseKey,
				},
			},
		});

		const responseData = {
			message: "Device removed successfully",
			devicesRemaining: license.devices.length - 1,
		};

		// Log the response for debugging
		logApiResponse(
			EndpointPrefix.LICENSE_REMOVE_DEVICE,
			200,
			responseData,
			context,
			`Device removed successfully`
		);

		res.json(responseData);
	} catch (error) {
		console.error("Error removing device:", error);

		if (
			error &&
			typeof error === "object" &&
			"code" in error &&
			typeof error.code === "string" &&
			error.code.startsWith("P") &&
			"name" in error &&
			"message" in error
		) {
			return handleDatabaseError(res, error as PrismaError);
		}

		sendErrorResponse(
			res,
			"Internal server error",
			500,
			ErrorCode.INTERNAL_ERROR,
			"An unexpected error occurred while removing the device",
		);
	}
});

/**
 * @route PUT /api/licenses/devices/metadata
 * @description Update device metadata for better user experience
 * @access Authenticated (Bearer token required)
 *
 * @param {Object} req.body - Request body
 * @param {string} req.body.deviceId - Device identifier
 * @param {Object} req.body.deviceMetadata - Device metadata to update
 * @param {string} [req.body.deviceMetadata.deviceName] - User-friendly device name
 * @param {string} [req.body.deviceMetadata.deviceType] - Device type (e.g., "MacBook Pro")
 * @param {string} [req.body.deviceMetadata.deviceModel] - Model details (e.g., "14-inch M2 2023")
 * @param {string} [req.body.deviceMetadata.operatingSystem] - OS version
 * @param {string} [req.body.deviceMetadata.architecture] - CPU architecture
 * @param {string} [req.body.deviceMetadata.screenResolution] - Display resolution
 * @param {string} [req.body.deviceMetadata.totalMemory] - RAM amount
 * @param {string} [req.body.deviceMetadata.userNickname] - Custom device nickname
 * @param {string} [req.body.deviceMetadata.location] - Device location
 * @param {string} [req.body.deviceMetadata.notes] - User notes about device
 *
 * @returns {Object} 200 - Device metadata updated successfully
 * @returns {string} returns.message - Success message
 * @returns {Object} returns.device - Updated device information
 *
 * @returns {Object} 400 - Validation error
 * @returns {Object} 401 - Invalid or missing device token
 * @returns {Object} 404 - Device not found
 * @returns {Object} 500 - Internal server error
 *
 * @example
 * PUT /api/licenses/devices/metadata
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * {
 *   "deviceId": "abc123def456",
 *   "deviceMetadata": {
 *     "deviceName": "John's MacBook Pro",
 *     "deviceType": "MacBook Pro",
 *     "deviceModel": "14-inch M2 2023",
 *     "userNickname": "Work Laptop",
 *     "location": "Office"
 *   }
 * }
 */
router.put("/devices/metadata", requestLoggingMiddleware(EndpointPrefix.LICENSE_UPDATE_DEVICE), async (req, res) => {
	try {
		const context = extractRequestContext(req);
		Logger.info(EndpointPrefix.LICENSE_UPDATE_DEVICE, "Processing device metadata update request", context);

		// Verify device token
		const authHeader = req.headers.authorization;
		if (!authHeader || !authHeader.startsWith("Bearer ")) {
			return res.status(401).json({ error: "Missing or invalid authorization header" });
		}

		const token = authHeader.substring(7);
		const payload = verifyDeviceToken(token) as {
			licenseId: string;
			deviceHash: string;
		} | null;
		if (!payload) {
			return res.status(401).json({ error: "Invalid device token" });
		}

		const { deviceId, deviceMetadata } = updateDeviceMetadataSchema.parse(req.body);

		Logger.info(EndpointPrefix.LICENSE_UPDATE_DEVICE, "Device metadata update requested", {
			...context,
			body: {
				deviceId: deviceId ? '[PROVIDED]' : '[NOT_PROVIDED]',
				deviceMetadata: deviceMetadata ? '[PROVIDED]' : '[NOT_PROVIDED]'
			}
		});

		// Find the device by hashing the provided deviceId with all possible salts
		const license = await prisma.license.findUnique({
			where: { id: payload.licenseId },
			include: { devices: true },
		});

		if (!license) {
			return res.status(404).json({ error: "License not found" });
		}

		let targetDevice = null;
		for (const device of license.devices) {
			const { hash: testHash } = hashDeviceId(deviceId, device.salt);
			if (testHash === device.deviceHash) {
				targetDevice = device;
				break;
			}
		}

		if (!targetDevice) {
			return res.status(404).json({ error: "Device not found" });
		}

		// Update device metadata
		const updateData: any = {};
		if (deviceMetadata.deviceName !== undefined) updateData.deviceName = deviceMetadata.deviceName;
		if (deviceMetadata.deviceType !== undefined) updateData.deviceType = deviceMetadata.deviceType;
		if (deviceMetadata.deviceModel !== undefined) updateData.deviceModel = deviceMetadata.deviceModel;
		if (deviceMetadata.operatingSystem !== undefined) updateData.operatingSystem = deviceMetadata.operatingSystem;
		if (deviceMetadata.architecture !== undefined) updateData.architecture = deviceMetadata.architecture;
		if (deviceMetadata.screenResolution !== undefined) updateData.screenResolution = deviceMetadata.screenResolution;
		if (deviceMetadata.totalMemory !== undefined) updateData.totalMemory = deviceMetadata.totalMemory;
		if (deviceMetadata.userNickname !== undefined) updateData.userNickname = deviceMetadata.userNickname;
		if (deviceMetadata.location !== undefined) updateData.location = deviceMetadata.location;
		if (deviceMetadata.notes !== undefined) updateData.notes = deviceMetadata.notes;

		const updatedDevice = await prisma.device.update({
			where: { id: targetDevice.id },
			data: updateData,
		});

		const responseData = {
			message: "Device metadata updated successfully",
			device: {
				id: updatedDevice.id,
				deviceName: updatedDevice.deviceName,
				deviceType: updatedDevice.deviceType,
				deviceModel: updatedDevice.deviceModel,
				operatingSystem: updatedDevice.operatingSystem,
				architecture: updatedDevice.architecture,
				screenResolution: updatedDevice.screenResolution,
				totalMemory: updatedDevice.totalMemory,
				userNickname: updatedDevice.userNickname,
				location: updatedDevice.location,
				notes: updatedDevice.notes,
				lastSeen: updatedDevice.lastSeen,
				appVersion: updatedDevice.appVersion,
			},
		};

		// Log the response for debugging
		logApiResponse(
			EndpointPrefix.LICENSE_UPDATE_DEVICE,
			200,
			responseData,
			context,
			`Device metadata updated for device: ${targetDevice.id}`
		);

		res.json(responseData);
	} catch (error) {
		console.error("Error updating device metadata:", error);

		if (error instanceof z.ZodError) {
			return res
				.status(400)
				.json({ error: "Invalid input data", details: error.message });
		}

		res.status(500).json({ error: "Internal server error" });
	}
});

export default router;

import { z } from "zod";

/**
 * Common validation patterns and constants
 */
const LICENSE_TYPES = ["trial", "standard", "extended"] as const;
const DEVICE_ID_MIN_LENGTH = 32;
const DEVICE_ID_MAX_LENGTH = 128;
const LICENSE_KEY_LENGTH = 24; // 24-character alphanumeric license key
const MAX_EMAIL_LENGTH = 254; // RFC 5321 limit
const MAX_APP_VERSION_LENGTH = 50;
const MAX_ADDITIONAL_DEVICES = 50;

/**
 * Device ID validation - accepts both hex strings and UUID formats for compatibility
 * This allows for gradual migration from UUID to SHA256 hex format
 */
const deviceIdSchema = z
	.string()
	.min(DEVICE_ID_MIN_LENGTH, {
		message: `Device ID must be at least ${DEVICE_ID_MIN_LENGTH} characters long`,
	})
	.max(DEVICE_ID_MAX_LENGTH, {
		message: `Device ID must be at most ${DEVICE_ID_MAX_LENGTH} characters long`,
	})
	.refine((val) => {
		// Accept pure hexadecimal strings (SHA256 format: 64 chars)
		const hexPattern = /^[a-fA-F0-9]+$/;
		// Accept UUID format (with hyphens: 36 chars)
		const uuidPattern = /^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/i;

		return hexPattern.test(val) || uuidPattern.test(val);
	}, {
		message: "Device ID must be a valid hexadecimal string or UUID format",
	})
	.transform((val) => {
		// Normalize UUIDs by removing hyphens and converting to lowercase
		// This ensures consistent storage regardless of input format
		return val.replace(/-/g, '').toLowerCase();
	});

/**
 * Email validation with enhanced rules
 */
const emailSchema = z
	.string()
	.min(1, { message: "Email address cannot be empty" })
	.max(MAX_EMAIL_LENGTH, {
		message: `Email address must be at most ${MAX_EMAIL_LENGTH} characters long`,
	})
	.email("Please provide a valid email address")
	.transform((val) => val.toLowerCase().trim()); // Normalize email

/**
 * License key validation - 24-character alphanumeric key
 */
const licenseKeySchema = z
	.string()
	.length(LICENSE_KEY_LENGTH, {
		message: `License key must be exactly ${LICENSE_KEY_LENGTH} characters long`,
	})
	.regex(/^[A-Z0-9]+$/, {
		message: "License key must contain only uppercase letters and numbers",
	});

/**
 * App version validation
 */
const appVersionSchema = z
	.string()
	.max(MAX_APP_VERSION_LENGTH, {
		message: `App version must be at most ${MAX_APP_VERSION_LENGTH} characters long`,
	})
	.regex(/^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+)?$/, {
		message:
			"App version must follow semantic versioning (e.g., 1.0.0 or 1.0.0-beta)",
	})
	.optional();

/**
 * License type validation
 */
const licenseTypeSchema = z.enum(LICENSE_TYPES);

/**
 * Stripe Payment Intent ID validation
 */
const stripePaymentIntentIdSchema = z
	.string()
	.regex(/^pi_[a-zA-Z0-9_]+$/, {
		message: "Invalid Stripe payment intent ID format",
	})
	.optional();

/**
 * Enhanced device metadata schema for better user experience
 */
const deviceMetadataSchema = z.object({
	deviceName: z.string().max(100).optional(),
	deviceType: z.string().max(50).optional(),
	deviceModel: z.string().max(50).optional(),
	operatingSystem: z.string().max(50).optional(),
	architecture: z.string().max(25).optional(),
	screenResolution: z.string().max(20).optional(),
	totalMemory: z.string().max(20).optional(),
	userNickname: z.string().max(50).optional(),
	location: z.string().max(50).optional(),
	notes: z.string().max(500).optional(),
});

/**
 * Schema for creating a new license
 * deviceId is optional to support paid licenses created via webhooks without initial device
 */
const createLicenseSchema = z.object({
	email: emailSchema,
	licenseType: licenseTypeSchema,
	deviceId: deviceIdSchema.optional(),
	stripePaymentIntentId: stripePaymentIntentIdSchema,
	deviceMetadata: deviceMetadataSchema.optional(),
});

/**
 * Schema for validating an existing license
 */
const validateLicenseSchema = z.object({
	licenseKey: licenseKeySchema,
	deviceId: deviceIdSchema,
	appVersion: appVersionSchema,
	deviceMetadata: deviceMetadataSchema.optional(),
});

/**
 * Schema for resending license information
 */
const resendLicenseSchema = z.object({
	email: emailSchema,
});

/**
 * Schema for upgrading a license (adding more devices)
 */
const upgradeLicenseSchema = z.object({
	licenseKey: licenseKeySchema,
	additionalDevices: z
		.number()
		.int({ message: "Additional devices must be a whole number" })
		.min(1, { message: "Must add at least 1 additional device" })
		.max(MAX_ADDITIONAL_DEVICES, {
			message: `Cannot add more than ${MAX_ADDITIONAL_DEVICES} devices at once`,
		}),
	stripePaymentIntentId: stripePaymentIntentIdSchema,
});

/**
 * Schema for updating device metadata
 */
const updateDeviceMetadataSchema = z.object({
	deviceId: deviceIdSchema,
	deviceMetadata: deviceMetadataSchema.required(),
});

/**
 * Schema for payment intent creation
 */
const createPaymentIntentSchema = z.object({
	licenseType: z.enum(["standard", "extended"]),
	additionalDevices: z
		.number()
		.int({ message: "Additional devices must be a whole number" })
		.min(0, { message: "Additional devices cannot be negative" })
		.max(MAX_ADDITIONAL_DEVICES, {
			message: `Cannot purchase more than ${MAX_ADDITIONAL_DEVICES} additional devices`,
		})
		.default(0),
	email: emailSchema,
});

/**
 * Schema for upgrade payment intent creation
 */
const createUpgradePaymentIntentSchema = z.object({
	licenseKey: licenseKeySchema,
	additionalDevices: z
		.number()
		.int({ message: "Additional devices must be a whole number" })
		.min(1, { message: "Must add at least 1 additional device" })
		.max(MAX_ADDITIONAL_DEVICES, {
			message: `Cannot add more than ${MAX_ADDITIONAL_DEVICES} devices at once`,
		}),
});

export {
	// Main schemas
	createLicenseSchema,
	validateLicenseSchema,
	resendLicenseSchema,
	upgradeLicenseSchema,
	createPaymentIntentSchema,
	createUpgradePaymentIntentSchema,
	deviceMetadataSchema,
	updateDeviceMetadataSchema,
	// Individual field schemas for reuse
	deviceIdSchema,
	emailSchema,
	licenseKeySchema,
	appVersionSchema,
	licenseTypeSchema,
	stripePaymentIntentIdSchema,
	// Constants
	LICENSE_TYPES,
	DEVICE_ID_MIN_LENGTH,
	DEVICE_ID_MAX_LENGTH,
	LICENSE_KEY_LENGTH,
	MAX_EMAIL_LENGTH,
	MAX_APP_VERSION_LENGTH,
	MAX_ADDITIONAL_DEVICES,
};

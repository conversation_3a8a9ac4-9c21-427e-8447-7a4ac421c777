{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "db:push": "prisma db push --schema ./prisma/schema", "db:studio": "prisma studio", "db:generate": "prisma generate --schema ./prisma/schema", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma db push --force-reset --schema ./prisma/schema && pnpm db:seed", "db:start": "docker compose up -d", "db:watch": "docker compose up", "db:stop": "docker compose stop", "db:down": "docker compose down", "stripe:listen": "stripe listen --forward-to http://localhost:3000/api/payments/webhook"}, "prisma": {"schema": "./schema"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.12.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.5", "stripe": "^18.3.0", "zod": "^4.0.10"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.13.11", "@types/nodemailer": "^6.4.17", "prisma": "^6.12.0", "tsdown": "^0.12.9", "tsx": "^4.19.2", "typescript": "^5.8.2"}}
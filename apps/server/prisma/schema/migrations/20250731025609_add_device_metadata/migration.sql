-- Create<PERSON>num
CREATE TYPE "LicenseType" AS ENUM ('trial', 'standard', 'extended');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "AuditAction" AS ENUM ('LICENSE_CREATED', 'LICENSE_VALIDATED', 'LICENSE_EXPIRED', 'DEVICE_ADDED', 'DEVICE_REMOVED', 'SUSPICIOUS_ACTIVITY', 'RATE_LIMIT_EXCEEDED', 'TRIAL_RESTRICTION_VIOLATED', 'TRIAL_ABUSE_DETECTED');

-- CreateTable
CREATE TABLE "licenses" (
    "id" TEXT NOT NULL,
    "licenseKey" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "licenseType" "LicenseType" NOT NULL,
    "maxDevices" INTEGER NOT NULL DEFAULT 2,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3),
    "stripePaymentIntentId" TEXT,
    "upgradePaymentIntentId" TEXT[] DEFAULT ARRAY[]::TEXT[],

    CONSTRAINT "licenses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "devices" (
    "id" TEXT NOT NULL,
    "licenseId" TEXT NOT NULL,
    "deviceHash" TEXT NOT NULL,
    "salt" TEXT NOT NULL,
    "firstSeen" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastSeen" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "appVersion" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "deviceName" TEXT,
    "deviceType" TEXT,
    "deviceModel" TEXT,
    "operatingSystem" TEXT,
    "architecture" TEXT,
    "screenResolution" TEXT,
    "totalMemory" TEXT,
    "userNickname" TEXT,
    "location" TEXT,
    "notes" TEXT,

    CONSTRAINT "devices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "audit_logs" (
    "id" TEXT NOT NULL,
    "action" "AuditAction" NOT NULL,
    "licenseKey" TEXT,
    "deviceHash" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "details" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "licenses_licenseKey_key" ON "licenses"("licenseKey");

-- CreateIndex
CREATE INDEX "licenses_email_idx" ON "licenses"("email");

-- CreateIndex
CREATE INDEX "licenses_licenseKey_idx" ON "licenses"("licenseKey");

-- CreateIndex
CREATE INDEX "licenses_expiresAt_idx" ON "licenses"("expiresAt");

-- CreateIndex
CREATE INDEX "devices_licenseId_idx" ON "devices"("licenseId");

-- CreateIndex
CREATE INDEX "devices_deviceHash_idx" ON "devices"("deviceHash");

-- CreateIndex
CREATE UNIQUE INDEX "devices_licenseId_deviceHash_key" ON "devices"("licenseId", "deviceHash");

-- CreateIndex
CREATE INDEX "audit_logs_licenseKey_idx" ON "audit_logs"("licenseKey");

-- CreateIndex
CREATE INDEX "audit_logs_createdAt_idx" ON "audit_logs"("createdAt");

-- AddForeignKey
ALTER TABLE "devices" ADD CONSTRAINT "devices_licenseId_fkey" FOREIGN KEY ("licenseId") REFERENCES "licenses"("id") ON DELETE CASCADE ON UPDATE CASCADE;

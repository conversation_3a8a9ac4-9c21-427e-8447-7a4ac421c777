generator client {
  provider     = "prisma-client"
  output       = "../generated"
  moduleFormat = "esm"
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

model License {
  id          String      @id @default(cuid())
  licenseKey  String      @unique
  email       String
  licenseType LicenseType
  maxDevices  Int         @default(2)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  expiresAt   DateTime?

  // Payment tracking
  stripePaymentIntentId  String?
  upgradePaymentIntentId String[] @default([])

  // Relationships
  devices Device[]

  @@index([email])
  @@index([licenseKey])
  @@index([expiresAt])
  @@map("licenses")
}

model Device {
  id         String   @id @default(cuid())
  licenseId  String
  deviceHash String // Hashed device ID for security
  salt       String // Salt used for hashing
  firstSeen  DateTime @default(now())
  lastSeen   DateTime @default(now())
  appVersion String?
  isActive   Boolean  @default(true)

  // Enhanced device metadata for better user experience
  deviceName       String? // User-friendly device name (e.g., "John's MacBook Pro")
  deviceType       String? // Device type (e.g., "MacBook Pro", "iMac", "Mac Studio")
  deviceModel      String? // Model details (e.g., "14-inch", "M2", "2023")
  operatingSystem  String? // OS version (e.g., "macOS 14.1", "macOS Sonoma")
  architecture     String? // CPU architecture (e.g., "arm64", "x86_64")
  screenResolution String? // Display resolution (e.g., "3024x1964", "5120x2880")
  totalMemory      String? // RAM amount (e.g., "16 GB", "32 GB")

  // Optional user-provided metadata
  userNickname String? // Custom name set by user (e.g., "Work Laptop", "Home Desktop")
  location     String? // Optional location (e.g., "Office", "Home")
  notes        String? // User notes about the device

  // Relationships
  license License @relation(fields: [licenseId], references: [id], onDelete: Cascade)

  @@unique([licenseId, deviceHash])
  @@index([licenseId])
  @@index([deviceHash])
  @@map("devices")
}

model AuditLog {
  id         String      @id @default(cuid())
  action     AuditAction
  licenseKey String?
  deviceHash String?
  ipAddress  String?
  userAgent  String?
  details    Json?
  createdAt  DateTime    @default(now())

  @@index([licenseKey])
  @@index([createdAt])
  @@map("audit_logs")
}

enum LicenseType {
  trial
  standard
  extended
}

enum AuditAction {
  LICENSE_CREATED
  LICENSE_VALIDATED
  LICENSE_EXPIRED
  DEVICE_ADDED
  DEVICE_REMOVED
  SUSPICIOUS_ACTIVITY
  RATE_LIMIT_EXCEEDED
  TRIAL_RESTRICTION_VIOLATED
  TRIAL_ABUSE_DETECTED
}

# Snapback Server API Documentation

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Rate Limiting](#rate-limiting)
4. [Error Handling](#error-handling)
5. [License Management Endpoints](#license-management-endpoints)
6. [Payment Processing Endpoints](#payment-processing-endpoints)
7. [Data Models](#data-models)
8. [Business Rules](#business-rules)

## Overview

The Snapback Server API provides license management and payment processing capabilities for the Snapback application. The API is built with Express.js and uses Stripe for payment processing.

**Base URL:** `https://localhost:3000/api`

**Supported HTTP Methods:** GET, POST, DELETE, OPTIONS

**Content Type:** `application/json`

## Authentication

### Device Token Authentication
Some endpoints require device token authentication via the `Authorization` header:

```
Authorization: Bearer <device_token>
```

Device tokens are JWT tokens generated during license validation and contain:
- `licenseId`: The license ID
- `deviceHash`: Hashed device identifier

## Rate Limiting

All API endpoints are protected by rate limiting:

- **General API Limit:** 100 requests per 15 minutes per IP
- **License Creation:** 5 requests per 15 minutes per IP
- **License Validation:** 30 requests per minute per IP
- **License Resend:** 3 requests per hour per IP
- **Payment Processing:** 10 requests per 15 minutes per IP

Rate limit headers are included in responses:
- `RateLimit-Limit`: Request limit per window
- `RateLimit-Remaining`: Remaining requests in current window
- `RateLimit-Reset`: Time when the rate limit resets

## Error Handling

### Standard Error Response Format

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "message": "Detailed error description",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/endpoint",
  "details": {
    "additional": "context"
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR`: Invalid input data
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `LICENSE_NOT_FOUND`: License key not found
- `LICENSE_EXPIRED`: License has expired
- `MAX_DEVICES_REACHED`: Device limit exceeded
- `TRIAL_ALREADY_USED_EMAIL`: Trial already used with email
- `TRIAL_ALREADY_USED_DEVICE`: Trial already used on device
- `PAYMENT_INCOMPLETE`: Payment not completed
- `UNAUTHORIZED`: Invalid or missing authentication
- `DATABASE_ERROR`: Database operation failed

## License Management Endpoints

### Create License

Creates a new license (trial or paid).

**Endpoint:** `POST /api/licenses/create`

**Rate Limit:** 5 requests per 15 minutes per IP

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "licenseType": "trial|standard|extended",
  "deviceId": "unique-device-identifier",
  "stripePaymentIntentId": "pi_1234567890" // Required for paid licenses
}
```

**Request Validation:**
- `email`: Valid email address (max 254 chars), normalized to lowercase
- `licenseType`: One of "trial", "standard", "extended"
- `deviceId`: 32-128 character alphanumeric string (optional for paid licenses)
- `stripePaymentIntentId`: Stripe payment intent ID format (required for paid licenses)

**Success Response (201):**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "licenseType": "standard",
  "expiresAt": null,
  "maxDevices": 2,
  "trialDaysRemaining": 0,
  "deviceToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Error Responses:**
- `400`: Validation error or payment incomplete
- `409`: License already exists for email or trial restrictions violated
- `429`: Rate limit exceeded
- `500`: Internal server error

**Business Rules:**
- Trial licenses: Limited to one per email and one per device
- Paid licenses: Lifetime duration (expiresAt: null)
- Standard licenses: 2 devices maximum
- Extended licenses: 5 devices maximum
- Payment verification required for paid licenses

### Validate License

Validates a license key and registers/authenticates a device.

**Endpoint:** `POST /api/licenses/validate`

**Rate Limit:** 30 requests per minute per IP

**Request Body:**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "deviceId": "unique-device-identifier",
  "appVersion": "1.0.0"
}
```

**Request Validation:**
- `licenseKey`: Exactly 24 characters, uppercase letters and numbers only
- `deviceId`: 32-128 character alphanumeric string
- `appVersion`: Semantic versioning format (optional)

**Success Response (200):**
```json
{
  "valid": true,
  "deviceToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "licenseType": "standard",
  "expiresAt": null,
  "maxDevices": 2,
  "devicesUsed": 1,
  "trialDaysRemaining": 0
}
```

**Error Responses:**
- `400`: Invalid input data
- `403`: License expired or device limit reached
- `404`: License not found
- `429`: Rate limit exceeded
- `500`: Internal server error

**Business Rules:**
- Automatically registers new devices if under limit
- Updates existing device's last seen timestamp and app version
- Returns device token for authenticated API access
- Calculates trial days remaining for trial licenses

### Resend License

Resends license information to the registered email address.

**Endpoint:** `POST /api/licenses/resend`

**Rate Limit:** 3 requests per hour per IP

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "message": "If a valid license exists for this email, it has been resent."
}
```

**Security Note:** Always returns success to prevent email enumeration attacks.

### Upgrade License

Adds additional device slots to an existing license.

**Endpoint:** `POST /api/licenses/upgrade`

**Rate Limit:** 5 requests per 15 minutes per IP

**Request Body:**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "additionalDevices": 2,
  "stripePaymentIntentId": "pi_1234567890"
}
```

**Success Response (200):**
```json
{
  "message": "License upgraded successfully",
  "newMaxDevices": 4,
  "additionalDevicesAdded": 2
}
```

### Get License Status

Retrieves detailed status information for a license.

**Endpoint:** `GET /api/licenses/status/{licenseKey}`

**Success Response (200):**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "licenseType": "standard",
  "email": "<EMAIL>",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "expiresAt": null,
  "maxDevices": 2,
  "devicesUsed": 1,
  "isExpired": false,
  "isActive": true,
  "trialDaysRemaining": 0,
  "devices": [
    {
      "id": "device-uuid",
      "firstSeen": "2024-01-01T00:00:00.000Z",
      "lastSeen": "2024-01-01T12:00:00.000Z",
      "appVersion": "1.0.0",
      "isActive": true
    }
  ]
}
```

### Remove Device

Removes a device from a license.

**Endpoint:** `DELETE /api/licenses/devices/{deviceId}`

**Authentication:** Required (Bearer token)

**Success Response (200):**
```json
{
  "message": "Device removed successfully"
}
```

**Error Responses:**
- `401`: Invalid or missing device token
- `404`: Device not found
- `500`: Internal server error

## Payment Processing Endpoints

### Get Pricing Information

Retrieves current pricing for all license types.

**Endpoint:** `GET /api/payments/pricing`

**Success Response (200):**
```json
{
  "trial": {
    "price": 0,
    "maxDevices": 1,
    "duration": "14 days"
  },
  "standard": {
    "price": 499,
    "maxDevices": 2,
    "duration": "Lifetime"
  },
  "extended": {
    "price": 999,
    "maxDevices": 5,
    "duration": "Lifetime"
  },
  "additionalDevice": {
    "price": 99,
    "description": "Per additional device"
  }
}
```

**Note:** Prices are in cents (USD).

### Create Payment Intent (Embedded Flow)

Creates a Stripe payment intent for embedded payment processing.

**Endpoint:** `POST /api/payments/create-payment-intent`

**Rate Limit:** 10 requests per 15 minutes per IP

**Request Body:**
```json
{
  "licenseType": "standard|extended",
  "additionalDevices": 0,
  "email": "<EMAIL>",
  "deviceId": "unique-device-identifier"
}
```

**Success Response (200):**
```json
{
  "clientSecret": "pi_1234567890_secret_abcdef",
  "amount": 499,
  "licenseType": "standard",
  "paymentIntentId": "pi_1234567890"
}
```

### Create Checkout Session (Redirect Flow)

Creates a Stripe checkout session for redirect-based payment processing.

**Endpoint:** `POST /api/payments/create-checkout-session`

**Request Body:**
```json
{
  "licenseType": "standard|extended",
  "additionalDevices": 0,
  "email": "<EMAIL>",
  "deviceId": "unique-device-identifier",
  "successUrl": "https://yoursite.com/success",
  "cancelUrl": "https://yoursite.com/cancel"
}
```

**Success Response (200):**
```json
{
  "sessionId": "cs_1234567890",
  "url": "https://checkout.stripe.com/pay/cs_1234567890",
  "amount": 499,
  "licenseType": "standard"
}
```

### Create Upgrade Payment Intent

Creates a payment intent for upgrading an existing license with additional devices.

**Endpoint:** `POST /api/payments/create-upgrade-payment-intent`

**Request Body:**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "additionalDevices": 2
}
```

**Success Response (200):**
```json
{
  "clientSecret": "pi_1234567890_secret_abcdef",
  "amount": 198,
  "additionalDevices": 2
}
```

### Stripe Webhook

Handles Stripe webhook events for payment processing.

**Endpoint:** `POST /api/payments/webhook`

**Content-Type:** `application/json` (raw body)

**Headers:**
- `stripe-signature`: Stripe webhook signature for verification

**Supported Events:**
- `payment_intent.succeeded`: Processes successful payments
- `payment_intent.payment_failed`: Handles failed payments
- `checkout.session.completed`: Processes completed checkout sessions

**Response (200):**
```json
{
  "received": true
}
```

### Get Checkout Session Status

Retrieves the status of a checkout session.

**Endpoint:** `GET /api/payments/checkout-session/{sessionId}`

**Success Response (200):**
```json
{
  "sessionId": "cs_1234567890",
  "paymentStatus": "paid",
  "customerEmail": "<EMAIL>",
  "amountTotal": 499,
  "currency": "usd",
  "license": {
    "licenseKey": "ABCD1234EFGH5678IJKL9012",
    "licenseType": "standard",
    "maxDevices": 2,
    "expiresAt": null,
    "email": "<EMAIL>"
  },
  "metadata": {
    "licenseType": "standard",
    "email": "<EMAIL>"
  }
}
```

### Get Payment Intent Status

Retrieves the status of a payment intent.

**Endpoint:** `GET /api/payments/payment-intent/{paymentIntentId}`

**Success Response (200):**
```json
{
  "paymentIntentId": "pi_1234567890",
  "status": "succeeded",
  "amount": 499,
  "currency": "usd",
  "license": {
    "licenseKey": "ABCD1234EFGH5678IJKL9012",
    "licenseType": "standard",
    "maxDevices": 2,
    "expiresAt": null,
    "email": "<EMAIL>"
  },
  "metadata": {
    "licenseType": "standard",
    "email": "<EMAIL>"
  }
}
```

## Data Models

### License Model

```typescript
interface License {
  id: string;
  licenseKey: string;          // 24-character alphanumeric key
  email: string;               // Normalized to lowercase
  licenseType: "trial" | "standard" | "extended";
  maxDevices: number;          // 1 for trial, 2 for standard, 5 for extended
  expiresAt: Date | null;      // null for lifetime licenses
  createdAt: Date;
  stripePaymentIntentId?: string;
  upgradePaymentIntentId?: string[];
  devices: Device[];
}
```

### Device Model

```typescript
interface Device {
  id: string;
  licenseId: string;
  deviceHash: string;          // Hashed device identifier
  salt: string;                // Salt used for hashing
  firstSeen: Date;
  lastSeen: Date;
  appVersion?: string;
  isActive: boolean;
}
```

### Error Response Model

```typescript
interface ErrorResponse {
  error: string;
  code?: string;
  message?: string;
  timestamp: string;
  path?: string;
  details?: Record<string, unknown>;
}
```

## Business Rules

### License Types and Restrictions

1. **Trial Licenses:**
   - Duration: 14 days from creation
   - Device limit: 1 device
   - Restrictions: One per email address, one per device
   - Cost: Free

2. **Standard Licenses:**
   - Duration: Lifetime (no expiration)
   - Device limit: 2 devices
   - Cost: $4.99 USD

3. **Extended Licenses:**
   - Duration: Lifetime (no expiration)
   - Device limit: 5 devices
   - Cost: $9.99 USD

### Device Management

- Devices are identified by a unique device identifier provided by the client
- Device identifiers are hashed with a random salt for security
- Each device registration updates the last seen timestamp
- Inactive devices can be removed to free up slots

### Payment Processing

- All payments processed through Stripe
- Two payment flows supported:
  - **Embedded Flow:** Payment intent → License creation via API
  - **Redirect Flow:** Checkout session → Automatic license creation
- Payment verification required for all paid licenses
- Upgrade payments add additional device slots

### Security Features

- Rate limiting on all endpoints
- Device token authentication for sensitive operations
- Request logging and audit trails
- Anti-spoofing middleware
- Webhook signature verification
- CORS protection with configurable origins

### Middleware Stack

1. **Helmet:** Security headers
2. **CORS:** Cross-origin resource sharing
3. **Rate Limiting:** Request throttling
4. **Body Parser:** JSON parsing (10MB limit)
5. **Request Logging:** Comprehensive logging
6. **Error Handling:** Standardized error responses

## Environment Configuration

The API requires the following environment variables:

- `PORT`: Server port (default: 3000)
- `CORS_ORIGIN`: Allowed CORS origins
- `STRIPE_SECRET_KEY`: Stripe secret key
- `STRIPE_WEBHOOK_SECRET`: Stripe webhook endpoint secret
- `DATABASE_URL`: PostgreSQL database connection string
- `JWT_SECRET`: Secret for device token signing
- `REQUEST_SIGNING_SECRET`: Secret for request signature verification

## HTTP Status Codes

- `200`: Success
- `201`: Created (license creation)
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid/missing authentication)
- `403`: Forbidden (license expired, device limit reached)
- `404`: Not Found (license/resource not found)
- `409`: Conflict (license already exists, trial restrictions)
- `429`: Too Many Requests (rate limit exceeded)
- `500`: Internal Server Error
